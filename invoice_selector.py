import sqlite3
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QMessageBox)
from PyQt5.QtCore import Qt
import os
import subprocess

class InvoiceSelector(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Invoice to Print")
        self.setGeometry(300, 300, 400, 150)
        self.setModal(True)
        
        # Connect to database
        self.conn = sqlite3.connect('invoices.db')
        self.cursor = self.conn.cursor()
        
        # Create layout
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)
        
        # Add invoice selection dropdown
        self.create_invoice_selector()
        
        # Add buttons
        self.create_buttons()
        
        # Load invoices
        self.load_invoices()
    
    def create_invoice_selector(self):
        """Create invoice selection dropdown"""
        # Label
        self.layout.addWidget(QLabel("Select an invoice to print:"))
        
        # Dropdown for invoices
        self.invoice_combo = QComboBox()
        self.invoice_combo.setMinimumWidth(300)
        self.layout.addWidget(self.invoice_combo)
        
        # Add some spacing
        self.layout.addSpacing(20)
    
    def create_buttons(self):
        """Create action buttons"""
        button_layout = QHBoxLayout()
        
        # Print button
        self.print_button = QPushButton("Print")
        self.print_button.clicked.connect(self.print_invoice)
        button_layout.addWidget(self.print_button)
        
        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        self.layout.addLayout(button_layout)
    
    def load_invoices(self):
        """Load invoices from database"""
        try:
            # Get all invoices with customer name and date
            self.cursor.execute("""
                SELECT invoice_number, customer_name, date 
                FROM invoices 
                ORDER BY invoice_number DESC
            """)
            
            invoices = self.cursor.fetchall()
            
            if not invoices:
                self.invoice_combo.addItem("No invoices found")
                self.print_button.setEnabled(False)
                return
            
            # Add invoices to dropdown
            for invoice in invoices:
                invoice_num, customer, date = invoice
                display_text = f"Invoice #{invoice_num} - {customer} - {date}"
                self.invoice_combo.addItem(display_text, invoice_num)
                
        except sqlite3.Error as e:
            QMessageBox.critical(self, "Database Error", f"Error loading invoices: {e}")
    
    def print_invoice(self):
        """Print the selected invoice"""
        if self.invoice_combo.count() == 0 or self.invoice_combo.currentText() == "No invoices found":
            return
        
        # Get selected invoice number
        invoice_number = self.invoice_combo.currentData()
        
        if not invoice_number:
            return
            
        # Check if PDF exists
        pdf_path = f"invoices/Invoice_{invoice_number}.pdf"
        
        if not os.path.exists(pdf_path):
            # Try to regenerate the invoice
            try:
                self.regenerate_invoice(invoice_number)
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Could not regenerate invoice: {e}")
                return
        
        # Open PDF with absolute path
        abs_path = os.path.abspath(pdf_path)
        try:
            # Use subprocess instead of webbrowser for better compatibility
            subprocess.Popen([abs_path], shell=True)
            self.accept()  # Close dialog after opening PDF
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Could not open PDF: {e}\nFile is saved at: {abs_path}")
    
    def regenerate_invoice(self, invoice_number):
        """Regenerate a PDF invoice from database data"""
        from fpdf import FPDF
        
        # Get invoice header data
        self.cursor.execute("""
            SELECT invoice_number, customer_name, customer_address, date, subtotal, tax, total, currency
            FROM invoices WHERE invoice_number = ?
        """, (invoice_number,))
        
        invoice_data = self.cursor.fetchone()
        if not invoice_data:
            raise Exception(f"Invoice {invoice_number} not found in database")
        
        # Get invoice items
        self.cursor.execute("""
            SELECT item_code, description, quantity, uom, rate, total
            FROM invoice_items WHERE invoice_number = ?
        """, (invoice_number,))
        
        items = self.cursor.fetchall()
        if not items:
            raise Exception(f"No items found for invoice {invoice_number}")
        
        # Unpack invoice data
        inv_number, customer_name, customer_address, date, subtotal, tax, total, currency = invoice_data
        
        # Replace Arabic currency symbol with 'AED' for compatibility with FPDF
        if currency == 'د.إ':
            currency = 'AED'
        
        # Generate PDF
        pdf = FPDF()  # Portrait orientation
        pdf.add_page()
        
        # Set font
        pdf.set_font("Arial", 'B', size=14)
        
        # Add company name
        pdf.cell(0, 10, "AL FARD ELECTRICAL TRADING-AL AIN", ln=1, align='C')
        
        # Add title
        pdf.set_font("Arial", 'B', size=12)
        pdf.cell(0, 10, f"INVOICE #{inv_number}", ln=1, align='C')
        pdf.ln(5)
        
        # Add invoice details
        pdf.set_font("Arial", size=10)
        pdf.cell(0, 7, f"Date: {date}", ln=1)
        pdf.cell(0, 7, f"Customer: {customer_name}", ln=1)
        pdf.cell(0, 7, f"Address: {customer_address}", ln=1)
        pdf.ln(10)
        
        # Add items table header
        pdf.set_font("Arial", 'B', 10)
        pdf.cell(30, 7, "Item Code", 1)
        pdf.cell(60, 7, "Description", 1)
        pdf.cell(15, 7, "Qty", 1)
        pdf.cell(15, 7, "UOM", 1)
        pdf.cell(25, 7, f"Rate ({currency})", 1)
        pdf.cell(25, 7, f"Total ({currency})", 1, ln=1)
        pdf.set_font("Arial", '', 10)
        
        # Add items
        for item in items:
            item_code, description, quantity, uom, rate, item_total = item
            
            pdf.cell(30, 7, item_code, 1)
            pdf.cell(60, 7, description, 1)
            pdf.cell(15, 7, str(quantity), 1)
            pdf.cell(15, 7, uom, 1)
            pdf.cell(25, 7, f"{rate:.2f}", 1)
            pdf.cell(25, 7, f"{item_total:.2f}", 1, ln=1)
        
        # Add totals
        pdf.ln(10)
        pdf.cell(145, 7, "Subtotal:", 0, 0, 'R')
        pdf.cell(25, 7, f"{currency}{subtotal:.2f}", 0, ln=1)
        
        # Calculate tax percentage
        tax_percent = (tax / subtotal) * 100 if subtotal > 0 else 5
        
        pdf.cell(145, 7, f"Tax ({tax_percent:.1f}%):", 0, 0, 'R')
        pdf.cell(25, 7, f"{currency}{tax:.2f}", 0, ln=1)
        
        pdf.set_font("Arial", 'B', 10)
        pdf.cell(145, 7, "Total:", 0, 0, 'R')
        pdf.cell(25, 7, f"{currency}{total:.2f}", 0, ln=1)
        
        # Save PDF
        if not os.path.exists("invoices"):
            os.makedirs("invoices")
            
        pdf_path = f"invoices/Invoice_{inv_number}.pdf"
        pdf.output(pdf_path)
        
        return pdf_path
    
    def closeEvent(self, event):
        """Handle dialog close"""
        self.conn.close()
        event.accept()
