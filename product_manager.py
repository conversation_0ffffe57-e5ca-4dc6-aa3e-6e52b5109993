import sqlite3
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QTableWidget, QTableWidgetItem, QComboBox,
                            QMessageBox, QHeaderView, QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QDoubleValidator, QFont

class ProductManager(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Product Manager")
        self.setGeometry(200, 200, 800, 500)

        # Connect to database
        self.conn = sqlite3.connect('invoices.db')
        self.cursor = self.conn.cursor()

        # Create layout
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # Create form for adding products
        self.create_form()

        # Create table for displaying products
        self.create_table()

        # Load products
        self.load_products()

    def create_form(self):
        """Create form for adding/editing products"""
        form_layout = QHBoxLayout()

        # Item Code
        item_code_layout = QVBoxLayout()
        item_code_layout.addWidget(QLabel("Item Code:"))
        self.item_code_input = QLineEdit()
        self.item_code_input.setPlaceholderText("Enter unique item code")
        item_code_layout.addWidget(self.item_code_input)
        form_layout.addLayout(item_code_layout)

        # Description
        description_layout = QVBoxLayout()
        description_layout.addWidget(QLabel("Description:"))
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("Enter item description")
        description_layout.addWidget(self.description_input)
        form_layout.addLayout(description_layout)

        # UOM
        uom_layout = QVBoxLayout()
        uom_layout.addWidget(QLabel("Unit of Measure:"))
        self.uom_input = QComboBox()
        self.uom_input.addItems(["each", "pcs", "kg", "liter", "hour", "day", "month", "set", "box", "roll", "meter"])
        self.uom_input.setCurrentIndex(0)  # Set "each" as the default selected unit
        uom_layout.addWidget(self.uom_input)
        form_layout.addLayout(uom_layout)

        # Rate
        rate_layout = QVBoxLayout()
        rate_layout.addWidget(QLabel("Rate (AED):"))
        self.rate_input = QLineEdit()
        self.rate_input.setValidator(QDoubleValidator(0, 999999, 2))
        self.rate_input.setPlaceholderText("0.00")
        rate_layout.addWidget(self.rate_input)
        form_layout.addLayout(rate_layout)

        # Add button
        self.add_button = QPushButton("Add Product")
        self.add_button.clicked.connect(self.add_product)
        form_layout.addWidget(self.add_button)

        # Clear button
        clear_button = QPushButton("Clear Form")
        clear_button.clicked.connect(self.clear_form)
        form_layout.addWidget(clear_button)

        self.layout.addLayout(form_layout)

    def create_table(self):
        """Create table for displaying products"""
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels([
            "Item Code", "Description", "UOM", "Rate (AED)", "Actions"
        ])
        self.products_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)

        self.layout.addWidget(self.products_table)

        # Add buttons at the bottom
        button_layout = QHBoxLayout()

        refresh_button = QPushButton("Refresh")
        refresh_button.clicked.connect(self.load_products)
        button_layout.addWidget(refresh_button)

        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        self.layout.addLayout(button_layout)

    def load_products(self):
        """Load products from database"""
        self.cursor.execute("SELECT item_code, description, uom, rate FROM products ORDER BY item_code")
        products = self.cursor.fetchall()

        self.products_table.setRowCount(0)

        for row_num, product in enumerate(products):
            self.products_table.insertRow(row_num)

            # Add product data
            self.products_table.setItem(row_num, 0, QTableWidgetItem(product[0]))
            self.products_table.setItem(row_num, 1, QTableWidgetItem(product[1]))
            self.products_table.setItem(row_num, 2, QTableWidgetItem(product[2]))
            self.products_table.setItem(row_num, 3, QTableWidgetItem(f"{product[3]:.2f}"))

            # Add edit and delete buttons
            button_layout = QHBoxLayout()
            button_layout.setContentsMargins(0, 0, 0, 0)
            button_widget = QWidget()

            edit_button = QPushButton("Edit")
            edit_button.clicked.connect(lambda _, r=row_num, p=product: self.edit_product(r, p))
            button_layout.addWidget(edit_button)

            delete_button = QPushButton("Delete")
            delete_button.clicked.connect(lambda _, code=product[0]: self.delete_product(code))
            button_layout.addWidget(delete_button)

            button_widget.setLayout(button_layout)
            self.products_table.setCellWidget(row_num, 4, button_widget)

    def add_product(self):
        """Add a new product to the database"""
        item_code = self.item_code_input.text().strip()
        description = self.description_input.text().strip()
        uom = self.uom_input.currentText()
        rate_text = self.rate_input.text().strip()

        # Validate inputs
        if not all([item_code, description, rate_text]):
            QMessageBox.warning(self, "Error", "Please fill all required fields")
            return

        try:
            rate = float(rate_text)
        except ValueError:
            QMessageBox.warning(self, "Error", "Invalid rate value")
            return

        try:
            # Check if item code already exists
            self.cursor.execute("SELECT COUNT(*) FROM products WHERE item_code=?", (item_code,))
            if self.cursor.fetchone()[0] > 0:
                # If in edit mode, update the product
                if hasattr(self, 'edit_mode') and self.edit_mode:
                    self.cursor.execute(
                        "UPDATE products SET description=?, uom=?, rate=? WHERE item_code=?",
                        (description, uom, rate, item_code)
                    )
                    self.conn.commit()
                    QMessageBox.information(self, "Success", f"Product {item_code} updated successfully")
                    self.edit_mode = False
                    self.add_button.setText("Add Product")
                else:
                    QMessageBox.warning(self, "Error", f"Item code {item_code} already exists")
                    return
            else:
                # Insert new product
                self.cursor.execute(
                    "INSERT INTO products (item_code, description, uom, rate) VALUES (?, ?, ?, ?)",
                    (item_code, description, uom, rate)
                )
                self.conn.commit()
                QMessageBox.information(self, "Success", f"Product {item_code} added successfully")

            # Clear form and reload products
            self.clear_form()
            self.load_products()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Database Error", f"An error occurred: {e}")

    def edit_product(self, row, product):
        """Load product data into form for editing"""
        self.edit_mode = True
        self.add_button.setText("Update Product")

        # Fill form with product data
        self.item_code_input.setText(product[0])
        self.item_code_input.setReadOnly(True)  # Don't allow changing item code
        self.description_input.setText(product[1])
        self.uom_input.setCurrentText(product[2])
        self.rate_input.setText(str(product[3]))

    def delete_product(self, item_code):
        """Delete a product from the database"""
        confirm = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete product {item_code}?",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                self.cursor.execute("DELETE FROM products WHERE item_code=?", (item_code,))
                self.conn.commit()
                QMessageBox.information(self, "Success", f"Product {item_code} deleted successfully")
                self.load_products()
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Database Error", f"An error occurred: {e}")

    def clear_form(self):
        """Clear the form fields"""
        self.item_code_input.clear()
        self.item_code_input.setReadOnly(False)
        self.description_input.clear()
        self.uom_input.setCurrentIndex(0)
        self.rate_input.clear()

        # Reset edit mode
        self.edit_mode = False
        self.add_button.setText("Add Product")

    def closeEvent(self, event):
        """Handle dialog close"""
        self.conn.close()
        event.accept()
