import sys
import os
from cx_Freeze import setup, Executable

# Dependencies
build_exe_options = {
    "packages": ["PyQt5", "fpdf", "sqlite3", "os", "sys", "datetime", "subprocess"],
    "excludes": [],
    "include_files": [
        "icon.ico",
        # Add any other files needed by your application
    ],
}

# Base for GUI applications
base = None
if sys.platform == "win32":
    base = "Win32GUI"

# Create the executable
executables = [
    Executable(
        "main.py",  # Your main script
        base=base,
        target_name="AL FARD Invoice System.exe",  # Name of the executable
        icon="icon.ico",  # Icon for the executable
        shortcut_name="AL FARD Invoice System",
        shortcut_dir="DesktopFolder",
    )
]

# Setup
setup(
    name="AL FARD Invoice System",
    version="1.0",
    description="Invoice Management System for AL FARD ELECTRICAL TRADING-AL AIN",
    options={"build_exe": build_exe_options},
    executables=executables,
)
