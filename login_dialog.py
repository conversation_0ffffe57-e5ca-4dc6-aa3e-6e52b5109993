from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import <PERSON>Font, QPixmap

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Login")
        self.setGeometry(300, 300, 400, 200)
        self.setModal(True)
        
        # Set fixed size and disable resizing
        self.setFixedSize(400, 200)
        
        # Create layout
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)
        
        # Add company name
        company_label = QLabel("AL FARD ELE.")
        font = company_label.font()
        font.setBold(True)
        font.setPointSize(12)
        company_label.setFont(font)
        company_label.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(company_label)
        
        # Add some spacing
        self.layout.addSpacing(20)
        
        # Username field
        username_layout = QHBoxLayout()
        username_label = QLabel("Username:")
        username_label.setFixedWidth(80)
        self.username_input = QLineEdit()
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        self.layout.addLayout(username_layout)
        
        # Password field
        password_layout = QHBoxLayout()
        password_label = QLabel("Password:")
        password_label.setFixedWidth(80)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        self.layout.addLayout(password_layout)
        
        # Add some spacing
        self.layout.addSpacing(20)
        
        # Login button
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        self.login_button = QPushButton("Login")
        self.login_button.setFixedWidth(100)
        self.login_button.clicked.connect(self.check_credentials)
        button_layout.addWidget(self.login_button)
        button_layout.addStretch()
        self.layout.addLayout(button_layout)
        
        # Set focus to username field
        self.username_input.setFocus()
        
        # Connect enter key to login button
        self.username_input.returnPressed.connect(self.login_button.click)
        self.password_input.returnPressed.connect(self.login_button.click)
    
    def check_credentials(self):
        """Check if the entered credentials are correct"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # Check credentials (hardcoded for now)
        if username == "sujeesh" and password == "sujeesh":
            self.accept()  # Close dialog with accept status
        else:
            QMessageBox.warning(self, "Login Failed", "Invalid username or password")
            self.password_input.clear()
            self.password_input.setFocus()
