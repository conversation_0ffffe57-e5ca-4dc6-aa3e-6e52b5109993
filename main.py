import sys
import sqlite3
import os
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QAction, QDialog, QCompleter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QDoubleValidator, QFont
from fpdf import FPDF
import subprocess
from product_manager import ProductManager
from invoice_selector import InvoiceSelector
from login_dialog import LoginDialog

class InvoiceApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Invoice Management System-AL FARD ELECTRICAL TRADING-AL AIN")
        self.setGeometry(100, 100, 1000, 700)

        # Initialize database
        self.init_db()

        # UI Theme and Currency
        self.dark_mode = False
        self.currency = "د.إ"

        # Set larger font size
        self.set_font_size()

        # Create main widget and layout
        self.main_widget = QWidget()
        self.setCentralWidget(self.main_widget)
        self.main_layout = QVBoxLayout()
        self.main_widget.setLayout(self.main_layout)

        # Create menu bar
        self.create_menu_bar()

        # Invoice header
        self.create_header_section()

        # Invoice items entry
        self.create_item_entry_section()

        # Invoice items table
        self.create_items_table()

        # Invoice actions
        self.create_action_buttons()

        # Apply initial style
        self.apply_style()

        # Initialize invoice number
        self.invoice_number = self.get_next_invoice_number()
        self.invoice_num_input.setText(str(self.invoice_number))

        # Setup description completer after UI is created
        self.setup_description_completer()

    def init_db(self):
        """Initialize SQLite database"""
        self.conn = sqlite3.connect('invoices.db')
        self.cursor = self.conn.cursor()

        # Create tables if they don't exist
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE,
                customer_name TEXT,
                customer_address TEXT,
                date TEXT,
                subtotal REAL,
                tax REAL,
                total REAL,
                currency TEXT
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                item_id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT,
                item_code TEXT,
                description TEXT,
                quantity REAL,
                uom TEXT,
                rate REAL,
                total REAL,
                FOREIGN KEY (invoice_number) REFERENCES invoices (invoice_number)
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                item_code TEXT PRIMARY KEY,
                description TEXT,
                uom TEXT,
                rate REAL
            )
        ''')

        self.conn.commit()

        # Insert some sample products if none exist
        self.cursor.execute("SELECT COUNT(*) FROM products")
        if self.cursor.fetchone()[0] == 0:
            sample_products = [
                ('DUMMY', 'dummy', 'pcs', 19.99)
                # ('P002', 'Standard Widget', 'pcs', 12.99),
                # ('P003', 'Deluxe Service', 'hour', 45.00),
                # ('P004', 'Basic Service', 'hour', 25.00),
                # ('P005', 'Bulk Material', 'kg', 8.50),
            ]
            self.cursor.executemany("INSERT INTO products VALUES (?, ?, ?, ?)", sample_products)
            self.conn.commit()

    def create_menu_bar(self):
        """Create menu bar with options"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        new_invoice_action = QAction('New Invoice', self)
        new_invoice_action.triggered.connect(self.new_invoice)
        file_menu.addAction(new_invoice_action)

        print_existing_action = QAction('Print Invoice', self)
        print_existing_action.triggered.connect(self.print_existing_invoice)
        file_menu.addAction(print_existing_action)

        exit_action = QAction('Exit', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # View menu
        view_menu = menubar.addMenu('View')

        dark_mode_action = QAction('Toggle Dark Mode', self)
        dark_mode_action.triggered.connect(self.toggle_dark_mode)
        view_menu.addAction(dark_mode_action)

        # Settings menu
        settings_menu = menubar.addMenu('Settings')

        # VAT settings
        vat_menu = settings_menu.addMenu('VAT')

        vat_5_action = QAction('5%', self)
        vat_5_action.triggered.connect(lambda: self.set_vat(5))
        vat_menu.addAction(vat_5_action)

        vat_0_action = QAction('0%', self)
        vat_0_action.triggered.connect(lambda: self.set_vat(0))
        vat_menu.addAction(vat_0_action)

        # Product Manager
        product_manager_action = QAction('Manage Products', self)
        product_manager_action.triggered.connect(self.open_product_manager)
        settings_menu.addAction(product_manager_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        # About action
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def create_header_section(self):
        """Create invoice header section"""
        # Company name at the top
        company_layout = QHBoxLayout()
        company_label = QLabel("AL FARD ELECTRICAL TRADING-AL AIN")
        font = company_label.font()
        font.setBold(True)
        font.setPointSize(12)
        company_label.setFont(font)
        company_label.setAlignment(Qt.AlignCenter)
        company_layout.addWidget(company_label)
        self.main_layout.addLayout(company_layout)

        # Header with invoice details
        header_layout = QHBoxLayout()

        # Invoice number
        invoice_num_layout = QVBoxLayout()
        invoice_num_layout.addWidget(QLabel("Invoice Number:"))
        self.invoice_num_input = QLineEdit()
        self.invoice_num_input.setReadOnly(True)
        invoice_num_layout.addWidget(self.invoice_num_input)
        header_layout.addLayout(invoice_num_layout)

        # Date
        date_layout = QVBoxLayout()
        date_layout.addWidget(QLabel("Date:"))
        self.date_input = QLineEdit(datetime.now().strftime("%d-%b-%Y"))
        date_layout.addWidget(self.date_input)
        header_layout.addLayout(date_layout)

        # Customer name
        customer_name_layout = QVBoxLayout()
        customer_name_layout.addWidget(QLabel("Customer Name:"))
        self.customer_name_input = QLineEdit()
        customer_name_layout.addWidget(self.customer_name_input)
        header_layout.addLayout(customer_name_layout)

        # Customer address
        customer_address_layout = QVBoxLayout()
        customer_address_layout.addWidget(QLabel("Customer Address:"))
        self.customer_address_input = QLineEdit()
        customer_address_layout.addWidget(self.customer_address_input)
        header_layout.addLayout(customer_address_layout)

        self.main_layout.addLayout(header_layout)

    def create_item_entry_section(self):
        """Create item entry form"""
        form_layout = QHBoxLayout()

        # Item Code (read-only, auto-filled)
        item_code_layout = QVBoxLayout()
        item_code_layout.addWidget(QLabel("Item Code:"))
        self.item_code_input = QLineEdit()
        self.item_code_input.setReadOnly(True)
        self.item_code_input.setPlaceholderText("Auto-filled from description")
        item_code_layout.addWidget(self.item_code_input)
        form_layout.addLayout(item_code_layout)

        # Description (searchable with auto-complete)
        description_layout = QVBoxLayout()
        description_layout.addWidget(QLabel("Description (Search):"))
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("Type to search item description...")
        self.setup_description_completer()
        description_layout.addWidget(self.description_input)
        form_layout.addLayout(description_layout)

        # Quantity
        quantity_layout = QVBoxLayout()
        quantity_layout.addWidget(QLabel("Quantity:"))
        self.quantity_input = QLineEdit()
        self.quantity_input.setValidator(QDoubleValidator(0, 9999, 2))
        quantity_layout.addWidget(self.quantity_input)
        form_layout.addLayout(quantity_layout)

        # UOM
        uom_layout = QVBoxLayout()
        uom_layout.addWidget(QLabel("Unit of Measure:"))
        self.uom_input = QComboBox()
        self.uom_input.addItems(["each", "pcs", "kg", "liter", "hour", "day", "month", "set", "box", "roll", "meter"])
        self.uom_input.setCurrentIndex(0)  # Set "each" as the default selected unit
        uom_layout.addWidget(self.uom_input)
        form_layout.addLayout(uom_layout)

        # Rate
        rate_layout = QVBoxLayout()
        rate_layout.addWidget(QLabel(f"Rate ({self.currency}):"))
        self.rate_input = QLineEdit()
        self.rate_input.setValidator(QDoubleValidator(0, 999999, 2))
        rate_layout.addWidget(self.rate_input)
        form_layout.addLayout(rate_layout)

        # Add Item button
        add_button = QPushButton("Add Item")
        add_button.clicked.connect(self.add_item)
        form_layout.addWidget(add_button)

        self.main_layout.addLayout(form_layout)

    def create_items_table(self):
        """Create table to display invoice items"""
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Item Code", "Description", "Quantity", "UOM",
            f"Rate ({self.currency})", "Total", "Actions"
        ])
        self.items_table.horizontalHeader().setStretchLastSection(True)

        self.main_layout.addWidget(self.items_table)

        # Initialize invoice totals
        self.totals_layout = QHBoxLayout()

        # Subtotal
        subtotal_layout = QVBoxLayout()
        subtotal_layout.addWidget(QLabel("Subtotal:"))
        self.subtotal_label = QLabel(f"{self.currency}0.00")
        subtotal_layout.addWidget(self.subtotal_label)
        self.totals_layout.addLayout(subtotal_layout)

        # Tax
        tax_layout = QVBoxLayout()
        tax_layout.addWidget(QLabel("Tax (%):"))
        self.tax_input = QLineEdit("5")
        self.tax_input.setValidator(QDoubleValidator(0, 100, 2))
        tax_layout.addWidget(self.tax_input)
        self.totals_layout.addLayout(tax_layout)

        # Tax Amount
        tax_amount_layout = QVBoxLayout()
        tax_amount_layout.addWidget(QLabel("Tax Amount:"))
        self.tax_amount_label = QLabel(f"{self.currency}0.00")
        tax_amount_layout.addWidget(self.tax_amount_label)
        self.totals_layout.addLayout(tax_amount_layout)

        # Total
        total_layout = QVBoxLayout()
        total_layout.addWidget(QLabel("Total:"))
        self.total_label = QLabel(f"{self.currency}0.00")
        total_layout.addWidget(self.total_label)
        self.totals_layout.addLayout(total_layout)

        self.main_layout.addLayout(self.totals_layout)

    def create_action_buttons(self):
        """Create action buttons (Save, Clear, etc.)"""
        button_layout = QHBoxLayout()

        # Save button
        save_button = QPushButton("Save Invoice")
        save_button.clicked.connect(self.save_invoice)
        button_layout.addWidget(save_button)

        # Clear button
        clear_button = QPushButton("Clear Invoice")
        clear_button.clicked.connect(self.new_invoice)
        button_layout.addWidget(clear_button)

        self.main_layout.addLayout(button_layout)

    def setup_description_completer(self):
        """Setup auto-complete functionality for description field"""
        # Get all descriptions from database
        self.cursor.execute("SELECT description FROM products ORDER BY description")
        descriptions = [row[0] for row in self.cursor.fetchall()]

        # Create completer
        self.description_completer = QCompleter(descriptions)
        self.description_completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.description_completer.setFilterMode(Qt.MatchContains)

        # Set completer to description input
        self.description_input.setCompleter(self.description_completer)

        # Connect to handle selection
        self.description_completer.activated.connect(self.on_description_selected)
        self.description_input.textChanged.connect(self.on_description_text_changed)

    def on_description_selected(self, description):
        """Handle when a description is selected from completer"""
        self.fetch_item_details_by_description(description)

    def on_description_text_changed(self, text):
        """Handle when description text changes"""
        # Clear other fields if text doesn't match any product
        if text.strip():
            self.cursor.execute("SELECT COUNT(*) FROM products WHERE description = ?", (text.strip(),))
            if self.cursor.fetchone()[0] == 0:
                # Text doesn't match exactly, clear auto-filled fields
                self.item_code_input.clear()
                self.uom_input.setCurrentIndex(0)
                self.rate_input.clear()
        else:
            # Text is empty, clear all fields
            self.item_code_input.clear()
            self.uom_input.setCurrentIndex(0)
            self.rate_input.clear()

    def fetch_item_details_by_description(self, description):
        """Auto-fill item details when description is selected"""
        if description.strip():
            self.cursor.execute("SELECT item_code, uom, rate FROM products WHERE description=?", (description.strip(),))
            result = self.cursor.fetchone()
            if result:
                self.item_code_input.setText(result[0])
                self.uom_input.setCurrentText(result[1])
                self.rate_input.setText(str(result[2]))

    def load_item_codes(self):
        """Refresh the description completer with updated product data"""
        self.setup_description_completer()



    def add_item(self):
        """Add item to the invoice table"""
        item_code = self.item_code_input.text().strip()
        description = self.description_input.text().strip()
        quantity = self.quantity_input.text().strip()
        uom = self.uom_input.currentText()
        rate = self.rate_input.text().strip()

        # Validate inputs
        if not all([item_code, description, quantity, rate]):
            QMessageBox.warning(self, "Error", "Please fill all required fields")
            return

        try:
            quantity = float(quantity)
            rate = float(rate)
            total = quantity * rate
        except ValueError:
            QMessageBox.warning(self, "Error", "Invalid quantity or rate")
            return

        # Add item to table
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        self.items_table.setItem(row, 0, QTableWidgetItem(item_code))
        self.items_table.setItem(row, 1, QTableWidgetItem(description))
        self.items_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
        self.items_table.setItem(row, 3, QTableWidgetItem(uom))
        self.items_table.setItem(row, 4, QTableWidgetItem(f"{rate:.2f}"))
        self.items_table.setItem(row, 5, QTableWidgetItem(f"{total:.2f}"))

        # Add delete button
        delete_button = QPushButton("Delete")
        delete_button.clicked.connect(lambda _, r=row: self.delete_item(r))
        self.items_table.setCellWidget(row, 6, delete_button)

        # Clear form
        self.item_code_input.clear()
        self.description_input.clear()
        self.quantity_input.clear()
        self.rate_input.clear()
        self.description_input.setFocus()  # Focus on description for next item

        # Update totals
        self.update_totals()

    def delete_item(self, row):
        """Delete item from the invoice table"""
        self.items_table.removeRow(row)
        self.update_totals()

    def update_totals(self):
        """Update subtotal, tax, and total amounts"""
        subtotal = 0.0

        for row in range(self.items_table.rowCount()):
            total_item = float(self.items_table.item(row, 5).text())
            subtotal += total_item

        try:
            tax_percent = float(self.tax_input.text())
        except ValueError:
            tax_percent = 0.0

        tax_amount = subtotal * (tax_percent / 100)
        total = subtotal + tax_amount

        self.subtotal_label.setText(f"{self.currency}{subtotal:.2f}")
        self.tax_amount_label.setText(f"{self.currency}{tax_amount:.2f}")
        self.total_label.setText(f"{self.currency}{total:.2f}")

    def save_invoice(self):
        """Save invoice to database and generate PDF"""
        # Validate inputs
        invoice_number = self.invoice_num_input.text().strip()
        customer_name = self.customer_name_input.text().strip()
        date = self.date_input.text().strip()

        if not all([invoice_number, customer_name, date]):
            QMessageBox.warning(self, "Error", "Please fill all required header fields")
            return

        if self.items_table.rowCount() == 0:
            QMessageBox.warning(self, "Error", "Please add at least one item to the invoice")
            return

        # Get totals
        subtotal = float(self.subtotal_label.text().replace(self.currency, ""))
        tax = float(self.tax_amount_label.text().replace(self.currency, ""))
        total = float(self.total_label.text().replace(self.currency, ""))

        # Save to database
        try:
            # Save invoice header
            self.cursor.execute('''
                INSERT INTO invoices (invoice_number, customer_name, customer_address, date, subtotal, tax, total, currency)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (invoice_number, customer_name, self.customer_address_input.text(), date, subtotal, tax, total, self.currency))

            # Save invoice items
            for row in range(self.items_table.rowCount()):
                item_code = self.items_table.item(row, 0).text()
                description = self.items_table.item(row, 1).text()
                quantity = float(self.items_table.item(row, 2).text())
                uom = self.items_table.item(row, 3).text()
                rate = float(self.items_table.item(row, 4).text())
                item_total = float(self.items_table.item(row, 5).text())

                self.cursor.execute('''
                    INSERT INTO invoice_items (invoice_number, item_code, description, quantity, uom, rate, total)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (invoice_number, item_code, description, quantity, uom, rate, item_total))

            self.conn.commit()

            # Generate PDF
            pdf_path = self.generate_pdf(invoice_number)

            # Show success message
            QMessageBox.information(self, "Success", f"Invoice saved and generated at:\n{pdf_path}")

            # Open PDF with absolute path
            abs_path = os.path.abspath(pdf_path)
            try:
                # Use subprocess instead of webbrowser for better compatibility
                subprocess.Popen([abs_path], shell=True)
            except Exception as e:
                QMessageBox.warning(self, "Error", f"Could not open PDF: {e}\nFile is saved at: {abs_path}")

            # Start new invoice
            self.new_invoice()

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Error", "Invoice number already exists")

    def generate_pdf(self, invoice_number):
        """Generate PDF invoice"""
        pdf = FPDF()  # Portrait orientation (default)
        pdf.add_page()

        # Use 'AED' for PDF instead of Arabic symbol to avoid encoding issues
        display_currency = 'AED:' if self.currency == 'د.إ' else self.currency

        # Set font
        pdf.set_font("Arial", 'B', size=14)

        # Add company name
        pdf.cell(0, 10, "AL FARD ELECTRICAL TRADING-AL AIN", ln=1, align='C')

        # Add title
        pdf.set_font("Arial", 'B', size=12)
        pdf.cell(0, 10, f"INVOICE #{invoice_number}", ln=1, align='C')
        pdf.ln(5)

        # Add invoice details
        pdf.set_font("Arial", size=10)
        pdf.cell(0, 7, f"Date: {self.date_input.text()}", ln=1)
        pdf.cell(0, 7, f"Customer: {self.customer_name_input.text()}", ln=1)
        pdf.cell(0, 7, f"Address: {self.customer_address_input.text()}", ln=1)
        pdf.ln(10)

        # Add items table header
        pdf.set_font("Arial", 'B', 10)
        pdf.cell(30, 7, "Item Code", 1)
        pdf.cell(60, 7, "Description", 1)
        pdf.cell(15, 7, "Qty", 1)
        pdf.cell(15, 7, "UOM", 1)
        pdf.cell(25, 7, f"Rate ({display_currency})", 1)
        pdf.cell(25, 7, f"Total ({display_currency})", 1, ln=1)
        pdf.set_font("Arial", '', 10)

        # Add items
        for row in range(self.items_table.rowCount()):
            item_code = self.items_table.item(row, 0).text()
            description = self.items_table.item(row, 1).text()
            quantity = self.items_table.item(row, 2).text()
            uom = self.items_table.item(row, 3).text()
            rate = self.items_table.item(row, 4).text()
            total = self.items_table.item(row, 5).text()

            pdf.cell(30, 7, item_code, 1)
            pdf.cell(60, 7, description, 1)
            pdf.cell(15, 7, quantity, 1)
            pdf.cell(15, 7, uom, 1)
            pdf.cell(25, 7, rate, 1)
            pdf.cell(25, 7, total, 1, ln=1)

        # Add totals
        pdf.ln(10)
        pdf.cell(145, 7, "Subtotal", 0, 0, 'R')
        # Extract numeric values and format with display_currency
        subtotal_value = float(self.subtotal_label.text().replace(self.currency, ""))
        tax_value = float(self.tax_amount_label.text().replace(self.currency, ""))
        total_value = float(self.total_label.text().replace(self.currency, ""))

        pdf.cell(25, 7, f"{display_currency}{subtotal_value:.2f}", 0, ln=1)

        pdf.cell(145, 7, f"Tax ({self.tax_input.text()}%)", 0, 0, 'R')
        pdf.cell(25, 7, f"{display_currency}{tax_value:.2f}", 0, ln=1)

        pdf.set_font("Arial", 'B', 10)
        pdf.cell(145, 7, "Total", 0, 0, 'R')
        pdf.cell(25, 7, f"{display_currency}{total_value:.2f}", 0, ln=1)

        # Save PDF
        if not os.path.exists("invoices"):
            os.makedirs("invoices")

        pdf_path = f"invoices/Invoice_{invoice_number}.pdf"
        pdf.output(pdf_path)

        return pdf_path

    def get_next_invoice_number(self):
        """Get the next available invoice number"""
        self.cursor.execute("SELECT MAX(invoice_number) FROM invoices")
        result = self.cursor.fetchone()[0]

        if result:
            try:
                # Check if it's our S-XXXX format
                if result.startswith('S-') and result[2:].isdigit():
                    # Extract the number part and increment
                    num = int(result[2:]) + 1
                    return f"S-{num}"
                # If invoice number is numeric, convert to S-format
                elif result.isdigit():
                    next_num = int(result) + 1
                    return f"S-{next_num}"
                else:
                    # For any other format, start with S-1000
                    return "S-1000"
            except ValueError:
                # If there's any error, start with S-1000
                return "S-1000"
        else:
            # First invoice
            return "S-1000"

    def new_invoice(self):
        """Clear current invoice and start new one"""
        # Clear inputs
        self.customer_name_input.clear()
        self.customer_address_input.clear()

        # Clear items table
        self.items_table.setRowCount(0)

        # Update invoice number
        self.invoice_number = self.get_next_invoice_number()
        self.invoice_num_input.setText(str(self.invoice_number))

        # Update date
        self.date_input.setText(datetime.now().strftime("%d-%b-%Y"))

        # Reset totals
        self.subtotal_label.setText(f"{self.currency}0.00")
        self.tax_amount_label.setText(f"{self.currency}0.00")
        self.total_label.setText(f"{self.currency}0.00")
        self.tax_input.setText("5")

        # Reset item inputs
        self.item_code_input.clear()
        self.description_input.clear()
        self.quantity_input.clear()
        self.rate_input.clear()

        # Refresh item codes list
        self.load_item_codes()

    def print_invoice(self):
        """Print the current invoice"""
        if self.items_table.rowCount() == 0:
            QMessageBox.warning(self, "Error", "No items to print")
            return

        # Generate PDF
        pdf_path = self.generate_pdf(self.invoice_num_input.text())

        # Open PDF with absolute path (this will open system print dialog)
        abs_path = os.path.abspath(pdf_path)
        try:
            # Use subprocess instead of webbrowser for better compatibility
            subprocess.Popen([abs_path], shell=True)
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Could not open PDF: {e}\nFile is saved at: {abs_path}")

    def print_existing_invoice(self):
        """Open dialog to select and print an existing invoice"""
        invoice_selector = InvoiceSelector(self)
        invoice_selector.exec_()

    def toggle_dark_mode(self):
        """Toggle between dark and light mode"""
        self.dark_mode = not self.dark_mode
        self.apply_style()

    def apply_style(self):
        """Apply dark or light style to the application"""
        if self.dark_mode:
            self.setStyleSheet("""
                QMainWindow, QWidget {
                    background-color: #333333;
                    color: #ffffff;
                }
                QLineEdit, QComboBox, QTableWidget {
                    background-color: #555555;
                    color: #ffffff;
                    border: 1px solid #777777;
                }
                QPushButton {
                    background-color: #666666;
                    color: #ffffff;
                    border: 1px solid #777777;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #777777;
                }
                QHeaderView::section {
                    background-color: #444444;
                    color: #ffffff;
                    padding: 5px;
                }
                QTableWidget {
                    gridline-color: #555555;
                }
                QMenuBar {
                    background-color: #444444;
                    color: #ffffff;
                }
                QMenuBar::item:selected {
                    background-color: #666666;
                }
                QMenu {
                    background-color: #444444;
                    color: #ffffff;
                }
                QMenu::item:selected {
                    background-color: #666666;
                }
            """)
        else:
            self.setStyleSheet("")

    def set_currency(self, currency):
        """Set the currency symbol"""
        self.currency = currency
        self.update_currency_display()

    def update_currency_display(self):
        """Update all currency displays"""
        # Update column header
        self.items_table.horizontalHeaderItem(4).setText(f"Rate ({self.currency})")

        # Update totals labels
        subtotal = self.subtotal_label.text().replace(self.subtotal_label.text()[0], self.currency)
        tax_amount = self.tax_amount_label.text().replace(self.tax_amount_label.text()[0], self.currency)
        total = self.total_label.text().replace(self.total_label.text()[0], self.currency)

        self.subtotal_label.setText(subtotal)
        self.tax_amount_label.setText(tax_amount)
        self.total_label.setText(total)

        # Update rate label
        for row in range(self.main_layout.count()):
            widget = self.main_layout.itemAt(row).widget()
            if isinstance(widget, QHBoxLayout):
                for i in range(widget.count()):
                    sub_layout = widget.itemAt(i)
                    if isinstance(sub_layout, QVBoxLayout):
                        label = sub_layout.itemAt(0).widget()
                        if isinstance(label, QLabel) and label.text().startswith("Rate"):
                            label.setText(f"Rate ({self.currency}):")

    def set_vat(self, vat_percent):
        """Set the VAT percentage"""
        self.tax_input.setText(str(vat_percent))
        self.update_totals()

    def open_product_manager(self):
        """Open the product manager dialog"""
        product_manager = ProductManager(self)
        product_manager.exec_()

        # Refresh description completer after dialog is closed
        self.load_item_codes()

    def set_font_size(self):
        """Set the application font size to 1.25 times larger"""
        app = QApplication.instance()
        default_font = app.font()
        default_size = default_font.pointSize()
        new_size = int(default_size * 1.25)  # Increase font size by 1.25 times

        # Create a new font with the larger size
        new_font = QFont(default_font.family(), new_size)

        # Apply the font to the entire application
        app.setFont(new_font)

    def show_about_dialog(self):
        """Show the About dialog"""
        about_text = """This software is developed by Akbar Kp for AL FARD ELECTRICAL TRADING - AL AIN.
Contact: <EMAIL>"""

        QMessageBox.about(self, "About Invoice Management System", about_text)

    def closeEvent(self, event):
        """Handle application close"""
        self.conn.close()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Show login dialog first
    login_dialog = LoginDialog()
    if login_dialog.exec_() == QDialog.Accepted:
        # If login successful, show the main application
        invoice_app = InvoiceApp()
        invoice_app.show()
        sys.exit(app.exec_())
    else:
        # If login failed or canceled, exit the application
        sys.exit(0)

