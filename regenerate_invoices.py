import sqlite3
import os
from fpdf import FPDF

def regenerate_invoice(invoice_number):
    """Regenerate a PDF invoice from database data"""
    # Connect to database
    conn = sqlite3.connect('invoices.db')
    cursor = conn.cursor()

    # Get invoice header data
    cursor.execute("""
        SELECT invoice_number, customer_name, customer_address, date, subtotal, tax, total, currency
        FROM invoices WHERE invoice_number = ?
    """, (invoice_number,))

    invoice_data = cursor.fetchone()
    if not invoice_data:
        print(f"Invoice {invoice_number} not found in database")
        return False

    # Get invoice items
    cursor.execute("""
        SELECT item_code, description, quantity, uom, rate, total
        FROM invoice_items WHERE invoice_number = ?
    """, (invoice_number,))

    items = cursor.fetchall()
    if not items:
        print(f"No items found for invoice {invoice_number}")
        return False

    # Unpack invoice data
    inv_number, customer_name, customer_address, date, subtotal, tax, total, currency = invoice_data

    # Replace Arabic currency symbol with 'AED' for compatibility with FPDF
    if currency == 'د.إ':
        currency = 'AED'

    # Generate PDF
    pdf = FPDF()
    pdf.add_page()

    # Set font
    pdf.set_font("Arial", 'B', size=14)

    # Add company name
    pdf.cell(0, 10, "AL FARD ELECTRICAL TRADING-AL AIN", ln=1, align='C')

    # Add title
    pdf.set_font("Arial", 'B', size=12)
    pdf.cell(0, 10, f"INVOICE #{inv_number}", ln=1, align='C')
    pdf.ln(5)

    # Add invoice details
    pdf.set_font("Arial", size=10)
    pdf.cell(0, 7, f"Date: {date}", ln=1)
    pdf.cell(0, 7, f"Customer: {customer_name}", ln=1)
    pdf.cell(0, 7, f"Address: {customer_address}", ln=1)
    pdf.ln(10)

    # Add items table header
    pdf.set_font("Arial", 'B', 10)
    pdf.cell(30, 7, "Item Code", 1)
    pdf.cell(60, 7, "Description", 1)
    pdf.cell(15, 7, "Qty", 1)
    pdf.cell(15, 7, "UOM", 1)
    pdf.cell(25, 7, f"Rate ({currency})", 1)
    pdf.cell(25, 7, f"Total ({currency})", 1, ln=1)
    pdf.set_font("Arial", '', 10)

    # Add items
    for item in items:
        item_code, description, quantity, uom, rate, item_total = item

        pdf.cell(30, 7, item_code, 1)
        pdf.cell(60, 7, description, 1)
        pdf.cell(15, 7, str(quantity), 1)
        pdf.cell(15, 7, uom, 1)
        pdf.cell(25, 7, f"{rate:.2f}", 1)
        pdf.cell(25, 7, f"{item_total:.2f}", 1, ln=1)

    # Add totals
    pdf.ln(10)
    pdf.cell(145, 7, "Subtotal:", 0, 0, 'R')
    pdf.cell(25, 7, f"{currency}{subtotal:.2f}", 0, ln=1)

    # Calculate tax percentage
    tax_percent = (tax / subtotal) * 100 if subtotal > 0 else 5

    pdf.cell(145, 7, f"Tax ({tax_percent:.1f}%):", 0, 0, 'R')
    pdf.cell(25, 7, f"{currency}{tax:.2f}", 0, ln=1)

    pdf.set_font("Arial", 'B', 10)
    pdf.cell(145, 7, "Total:", 0, 0, 'R')
    pdf.cell(25, 7, f"{currency}{total:.2f}", 0, ln=1)

    # Save PDF
    if not os.path.exists("invoices"):
        os.makedirs("invoices")

    pdf_path = f"invoices/Invoice_{inv_number}.pdf"
    pdf.output(pdf_path)

    print(f"Invoice {inv_number} regenerated successfully at {pdf_path}")
    return True

def main():
    # Regenerate missing invoices
    print("Regenerating missing invoices...")
    regenerate_invoice('1002')
    regenerate_invoice('1004')
    print("Done!")

if __name__ == "__main__":
    main()
