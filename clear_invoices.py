import sqlite3
import os
import shutil

def clear_invoices():
    """Clear all invoice records from the database and delete PDF files"""
    try:
        # Connect to database
        conn = sqlite3.connect('invoices.db')
        cursor = conn.cursor()
        
        # Delete all records from invoice_items table
        cursor.execute("DELETE FROM invoice_items")
        
        # Delete all records from invoices table
        cursor.execute("DELETE FROM invoices")
        
        # Commit changes
        conn.commit()
        
        # Close connection
        conn.close()
        
        print("All invoice records deleted from database.")
        
        # Delete all invoice PDF files
        if os.path.exists("invoices"):
            # Option 1: Delete the entire directory and recreate it
            shutil.rmtree("invoices")
            os.makedirs("invoices")
            print("All invoice PDF files deleted.")
        
        print("Invoice clearing completed successfully.")
        return True
    except Exception as e:
        print(f"Error clearing invoices: {e}")
        return False

if __name__ == "__main__":
    # Ask for confirmation
    confirm = input("This will delete ALL invoice records and PDF files. Are you sure? (y/n): ")
    if confirm.lower() == 'y':
        clear_invoices()
    else:
        print("Operation canceled.")
